# MCTS Agent for Water Fight Game (SoakOverflow)

This is a C++ implementation of a Monte Carlo Tree Search (MCTS) agent for the water fight game. The agent uses advanced AI techniques to make strategic decisions within the 50ms time limit.

## Features

- **Monte Carlo Tree Search Algorithm**: Uses MCTS with UCB1 selection for optimal decision making
- **High-Precision Timer**: Microsecond-precision timing to stay under the 50ms limit
- **Configurable Time Management**: Adjustable time cutoff with safety margin (default 46ms)
- **Territory-Based Scoring**: Prioritizes territory control as the primary objective
- **Wetness Concentration Strategy**: Prefers eliminating agents over spreading damage
- **Multiple Strategy Generation**: Implements aggressive, defensive, and balanced action strategies
- **Robust Error Handling**: Graceful fallbacks and exception handling

## Compilation

```bash
g++ -O2 -std=c++17 cpp_agent_1.cpp -o cpp_agent_1
```

## Usage

### Basic Usage
```bash
./cpp_agent_1
```

### With Custom Time Limit
```bash
./cpp_agent_1 45.0    # Sets time limit to 45ms
./cpp_agent_1 48.0    # Sets time limit to 48ms
```

## Algorithm Details

### MCTS Implementation
1. **Selection**: Uses UCB1 formula to select promising nodes
2. **Expansion**: Generates new game states from untried actions
3. **Simulation**: Runs random playouts to evaluate positions
4. **Backpropagation**: Updates node statistics with simulation results

### Scoring Heuristics
- **Territory Control** (Weight: 10x): Primary scoring factor based on grid control
- **Wetness Concentration** (Weight: 2x): Bonus for concentrating damage to eliminate agents
- **Agent Count Advantage** (Weight: 50 per agent): Bonus for having more agents alive

### Action Strategies
1. **Aggressive**: Prioritizes shooting and throwing over movement
2. **Defensive**: Moves damaged agents to safety, shoots with healthy agents
3. **Balanced**: Alternates between different action types for variety

## Performance Optimizations

- Limited action combinations to maintain performance
- Early termination when good solutions are found
- Efficient state copying and memory management
- Optimized simulation depth (5 turns max)

## Time Management

The agent includes sophisticated time management:
- Configurable time limit (default 46ms for safety)
- High-precision microsecond timer
- Early termination at 80% of time limit
- Graceful fallback to default actions if time runs out

## Error Handling

- Robust input parsing with error recovery
- Fallback to safe "hunker down" actions if MCTS fails
- Exception handling in simulation phase
- Memory management with proper cleanup

## Testing

A test compilation script is included:
```bash
g++ -O2 -std=c++17 test_agent.cpp -o test_agent && ./test_agent
```

## Game Mechanics Supported

- All agent types (GUNNER, SNIPER, BOMBER, ASSAULT, BERSERKER)
- Movement with collision detection
- Shooting with range and cover calculations
- Splash bomb throwing
- Territory control calculation
- Wetness damage system
- Cooldown management

## Notes

- The agent is designed to be conservative and avoid risky moves
- Prefers coordinated strategies over individual agent optimization
- Implements the complete game rule set as specified in the game description
- Optimized for the 50ms time constraint while maintaining decision quality
