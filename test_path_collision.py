#!/usr/bin/env python3

# Test the new path collision detection logic
from collections import deque

def get_adjacent_tiles(x, y, width, height):
    """Get adjacent tiles (up, down, left, right)."""
    adjacent = []
    for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            adjacent.append((nx, ny))
    return adjacent

def can_move_to_tile(x, y, grid, agents):
    """Check if a tile is passable (no cover, no other agents)."""
    # Check for cover
    tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
    if tile and tile['tile_type'] > 0:
        return False
    # Check for other agents
    for agent in agents:
        if agent['x'] == x and agent['y'] == y:
            return False
    return True

def has_valid_path(from_pos, to_pos, blocked_positions, grid, width, height):
    """Check if there's a valid path from from_pos to to_pos avoiding blocked positions."""
    if from_pos == to_pos:
        return True

    # Use BFS to find if path exists (similar to server's A* but simpler)
    queue = deque([from_pos])
    visited = {from_pos}

    while queue:
        current = queue.popleft()

        # Check all adjacent positions
        for next_pos in get_adjacent_tiles(current[0], current[1], width, height):
            if next_pos in visited:
                continue

            # Check if position is blocked by static agent
            if next_pos in blocked_positions:
                continue

            # Check if position has cover
            if not can_move_to_tile(next_pos[0], next_pos[1], grid, []):
                continue

            # Found target
            if next_pos == to_pos:
                return True

            visited.add(next_pos)
            queue.append(next_pos)

    return False

def test_path_collision():
    print("Testing path collision detection...")
    
    # Create a simple 4x4 grid
    grid = []
    for y in range(4):
        for x in range(4):
            grid.append({'x': x, 'y': y, 'tile_type': 0})
    
    width, height = 4, 4
    
    # Test case 1: Direct path blocked (your specific scenario)
    # Agent wants to move from (2,1) to (2,3) but there's an agent at (2,2)
    # In a narrow corridor, this should fail
    blocked_positions = {(2, 2)}
    result1 = has_valid_path((2, 1), (2, 3), blocked_positions, grid, width, height)
    print(f"Test 1 - Path (2,1) to (2,3) with agent at (2,2): {result1}")

    # Test case 1b: Same scenario but with walls to create a narrow corridor
    # Create a corridor by adding walls
    grid_with_walls = []
    for y in range(4):
        for x in range(4):
            tile_type = 0
            # Add walls to create a narrow corridor at x=2
            if x == 1 and y in [1, 2, 3]:
                tile_type = 1  # Wall
            if x == 3 and y in [1, 2, 3]:
                tile_type = 1  # Wall
            grid_with_walls.append({'x': x, 'y': y, 'tile_type': tile_type})

    result1b = has_valid_path((2, 1), (2, 3), blocked_positions, grid_with_walls, width, height)
    print(f"Test 1b - Path (2,1) to (2,3) in narrow corridor with agent at (2,2): {result1b}")
    
    # Test case 2: Alternative path available
    # Agent wants to move from (0,0) to (2,0) but there's an agent at (1,0)
    # Should be able to go around via (0,1) -> (1,1) -> (2,1) -> (2,0)
    blocked_positions = {(1, 0)}
    result2 = has_valid_path((0, 0), (2, 0), blocked_positions, grid, width, height)
    print(f"Test 2 - Path (0,0) to (2,0) with agent at (1,0): {result2}")
    
    # Test case 3: No path available (completely blocked)
    # Agent wants to move from (1,1) to (1,3) but agents block all paths
    blocked_positions = {(0, 1), (1, 2), (2, 1)}
    result3 = has_valid_path((1, 1), (1, 3), blocked_positions, grid, width, height)
    print(f"Test 3 - Path (1,1) to (1,3) with multiple blocking agents: {result3}")
    
    # Test case 4: Adjacent move (should always work unless target is blocked)
    blocked_positions = {(2, 2)}
    result4 = has_valid_path((2, 1), (2, 2), blocked_positions, grid, width, height)
    print(f"Test 4 - Adjacent move to blocked position: {result4}")
    
    # Test case 5: Adjacent move to free position
    blocked_positions = {(2, 2)}
    result5 = has_valid_path((2, 1), (1, 1), blocked_positions, grid, width, height)
    print(f"Test 5 - Adjacent move to free position: {result5}")

if __name__ == "__main__":
    test_path_collision()
