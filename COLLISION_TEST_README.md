# Collision Testing Code

This directory contains temporary testing code to explore movement collision rules in the game.

## Files Added

1. **Modified agent.py** - Added collision testing strategy (marked with comments for easy removal)
2. **toggle_collision_test.py** - <PERSON><PERSON>t to easily enable/disable testing mode
3. **collision_test_input.txt** - Sample input file for testing
4. **COLLISION_TEST_README.md** - This file

## How to Use

### Quick Demo
```bash
python3 demo_collision_test.py
```

### Enable Collision Testing
```bash
python3 toggle_collision_test.py on
```

### Run the Test
```bash
python3 agent.py < test_forced_collision_input.txt
```

### Disable Collision Testing
```bash
python3 toggle_collision_test.py off
```

## What the Test Does

The collision testing strategy attempts to create a specific scenario:

1. **Setup Phase**: Moves 2 agents to positions (0,1) and (0,3)
2. **Collision Test Phase**: Once in position, both agents simultaneously attempt to move:
   - Agent at (0,1) tries to move to (0,4) 
   - Agent at (0,3) tries to move to (0,0)

This creates a scenario where the agents' paths would visually cross each other, but their start and end positions don't directly conflict.

## Expected Behavior

Based on the collision detection code in agent.py, this should test whether:
- The server allows moves where paths cross visually but start/end positions don't conflict
- Or if there are additional collision rules we haven't discovered

## Debug Output

Watch the stderr output for debug messages like:
- `DEBUG: COLLISION TESTING MODE ACTIVE`
- `DEBUG: Setup phase - moving agents to test positions`
- `DEBUG: COLLISION TEST PHASE - Both agents attempting simultaneous moves!`
- `DEBUG: Our collision detection predicts: ...`

## Clean Removal

To completely remove the testing code:

1. Disable testing mode: `python3 toggle_collision_test.py off`
2. Remove the testing code sections from agent.py (search for "===== TESTING CODE")
3. Delete the testing files: `rm toggle_collision_test.py collision_test_input.txt COLLISION_TEST_README.md`

## Testing Code Locations in agent.py

The testing code is clearly marked with comments:
- Lines ~11-112: `execute_collision_test_strategy()` function
- Lines ~2137-2141: Strategy selection override
- Lines ~2152-2155: Strategy execution

All testing code is wrapped in `# ===== TESTING CODE - EASY TO REMOVE =====` comments.
