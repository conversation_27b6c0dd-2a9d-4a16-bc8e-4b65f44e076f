#!/usr/bin/env python3
"""
Test script to verify splash bomb safety improvements work correctly.
"""
import sys

# Copy the functions we need to test directly to avoid import issues
def manhattan_distance(x1, y1, x2, y2):
    return abs(x1 - x2) + abs(y1 - y2)

def get_splash_area(x, y, width, height):
    ALL_DIRECTIONS = [(0, 1), (0, -1), (1, 0), (-1, 0), (1, 1), (1, -1), (-1, 1), (-1, -1)]
    return [(x + dx, y + dy) for dx, dy in [(0, 0)] + ALL_DIRECTIONS
            if 0 <= x + dx < width and 0 <= y + dy < height]

def is_safe_splash_target(target_x, target_y, my_agents, width, height):
    splash_area = get_splash_area(target_x, target_y, width, height)
    return not any((agent['x'], agent['y']) in splash_area for agent in my_agents)

def find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, width, height):
    MAX_THROW_RANGE = 4
    SPLASH_DAMAGE = 30
    best_targets = []

    for target_x in range(max(0, agent_x - MAX_THROW_RANGE), min(width, agent_x + MAX_THROW_RANGE + 1)):
        for target_y in range(max(0, agent_y - MAX_THROW_RANGE), min(height, agent_y + MAX_THROW_RANGE + 1)):
            distance = manhattan_distance(agent_x, agent_y, target_x, target_y)
            if distance > MAX_THROW_RANGE or not is_safe_splash_target(target_x, target_y, my_agents, width, height):
                continue

            splash_area = get_splash_area(target_x, target_y, width, height)
            enemies_hit = [enemy for enemy in enemy_agents if (enemy['x'], enemy['y']) in splash_area]

            if enemies_hit:
                total_enemy_wetness = sum(enemy.get('wetness', 0) for enemy in enemies_hit)
                elimination_potential = sum(1 for enemy in enemies_hit if enemy.get('wetness', 0) + SPLASH_DAMAGE >= 100)

                # Calculate safety penalty - heavily penalize throws near friendly agents
                safety_penalty = 0
                min_friendly_distance = float('inf')
                for agent in my_agents:
                    agent_distance = manhattan_distance(target_x, target_y, agent['x'], agent['y'])
                    min_friendly_distance = min(min_friendly_distance, agent_distance)

                    # Strong penalty for throws near friendlies (even if not hitting directly)
                    if agent_distance <= 2:  # Within 2 tiles of splash center
                        safety_penalty += 10000  # Massive penalty
                    elif agent_distance <= 3:  # Within 3 tiles
                        safety_penalty += 1000   # Large penalty

                best_targets.append({
                    'target_pos': (target_x, target_y),
                    'distance': distance,
                    'enemies_hit': enemies_hit,
                    'total_damage': len(enemies_hit) * SPLASH_DAMAGE,
                    'enemy_count': len(enemies_hit),
                    'elimination_potential': elimination_potential,
                    'total_enemy_wetness': total_enemy_wetness,
                    'safety_penalty': safety_penalty,
                    'min_friendly_distance': min_friendly_distance
                })

    # Sort with safety as highest priority, then effectiveness
    best_targets.sort(key=lambda x: (x['safety_penalty'], -x['elimination_potential'], -x['enemy_count'], -x['total_enemy_wetness'], x['distance']))

    return best_targets

def test_friendly_fire_prevention():
    """Test that splash bombs avoid hitting friendly agents."""
    print("Testing friendly fire prevention...")
    
    # Setup: Agent at (5,5), friendly at (7,7), enemy at (8,8)
    agent_x, agent_y = 5, 5
    my_agents = [
        {'x': 5, 'y': 5, 'agent_id': 1},  # The throwing agent
        {'x': 7, 'y': 7, 'agent_id': 2}   # Friendly agent
    ]
    enemy_agents = [
        {'x': 8, 'y': 8, 'agent_id': 10, 'wetness': 0}  # Enemy
    ]
    
    targets = find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, 15, 15)
    
    print(f"Found {len(targets)} potential targets")
    
    for i, target in enumerate(targets[:3]):  # Show top 3 targets
        target_x, target_y = target['target_pos']
        print(f"Target {i+1}: ({target_x},{target_y}) - "
              f"enemies_hit={target['enemy_count']}, "
              f"safety_penalty={target.get('safety_penalty', 0)}, "
              f"min_friendly_dist={target.get('min_friendly_distance', 'N/A')}")
        
        # Check if this target would hit the friendly agent at (7,7)
        splash_area = [(target_x + dx, target_y + dy) 
                      for dx in [-1, 0, 1] for dy in [-1, 0, 1]]
        friendly_hit = (7, 7) in splash_area
        print(f"  Would hit friendly: {friendly_hit}")
        
        if friendly_hit:
            print("  ERROR: Target would hit friendly agent!")
        else:
            print("  ✓ Safe from friendly fire")
    
    # Verify that targets hitting friendlies have high safety penalties
    unsafe_targets = [t for t in targets if t.get('safety_penalty', 0) > 0]
    safe_targets = [t for t in targets if t.get('safety_penalty', 0) == 0]
    
    print(f"\nSafe targets: {len(safe_targets)}")
    print(f"Unsafe targets (with penalties): {len(unsafe_targets)}")
    
    if targets:
        best_target = targets[0]
        print(f"\nBest target safety penalty: {best_target.get('safety_penalty', 0)}")
        if best_target.get('safety_penalty', 0) == 0:
            print("✓ Best target is safe!")
        else:
            print("⚠ Best target has safety penalty - this might be acceptable if no safe options exist")

def test_multiple_enemy_scenario():
    """Test scenario with multiple enemies to ensure we pick the safest option."""
    print("\n" + "="*50)
    print("Testing multiple enemy scenario...")
    
    # Agent at (5,5), friendly at (6,6), enemies at various positions
    agent_x, agent_y = 5, 5
    my_agents = [
        {'x': 5, 'y': 5, 'agent_id': 1},  # The throwing agent
        {'x': 6, 'y': 6, 'agent_id': 2}   # Friendly agent (close!)
    ]
    enemy_agents = [
        {'x': 7, 'y': 7, 'agent_id': 10, 'wetness': 0},  # Enemy near friendly
        {'x': 9, 'y': 5, 'agent_id': 11, 'wetness': 0},  # Enemy far from friendly
        {'x': 8, 'y': 8, 'agent_id': 12, 'wetness': 70}  # High wetness enemy near friendly
    ]
    
    targets = find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, 15, 15)
    
    print(f"Found {len(targets)} potential targets")
    
    for i, target in enumerate(targets[:5]):  # Show top 5 targets
        target_x, target_y = target['target_pos']
        print(f"Target {i+1}: ({target_x},{target_y}) - "
              f"enemies_hit={target['enemy_count']}, "
              f"elimination_potential={target['elimination_potential']}, "
              f"safety_penalty={target.get('safety_penalty', 0)}")
    
    if targets:
        best_target = targets[0]
        print(f"\nBest target chosen: {best_target['target_pos']}")
        print(f"Safety penalty: {best_target.get('safety_penalty', 0)}")
        print(f"Enemies hit: {best_target['enemy_count']}")
        
        # Check distance to friendly
        target_x, target_y = best_target['target_pos']
        friendly_distance = manhattan_distance(target_x, target_y, 6, 6)
        print(f"Distance to friendly agent: {friendly_distance}")
        
        if friendly_distance >= 2:
            print("✓ Best target maintains safe distance from friendly!")
        else:
            print("⚠ Best target is close to friendly - check if this is necessary")

if __name__ == "__main__":
    test_friendly_fire_prevention()
    test_multiple_enemy_scenario()
    print("\n" + "="*50)
    print("Splash bomb safety tests completed!")
