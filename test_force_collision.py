#!/usr/bin/env python3

# Create a test scenario that forces collision by manually setting positions
import sys

# Create input that will force both agents to want position (2,2)
test_input = """1
4
1 1 2 3 25 2
2 1 2 3 25 2
3 2 2 3 25 0
4 2 2 3 25 0
5 5
0 0 0 1 0 0 2 0 0 3 0 0 4 0 0
0 1 0 1 1 0 2 1 0 3 1 0 4 1 0
0 2 0 1 2 0 2 2 0 3 2 0 4 2 0
0 3 0 1 3 0 2 3 0 3 3 0 4 3 0
0 4 0 1 4 0 2 4 0 3 4 0 4 4 0
4
1 1 1 0 2 0
2 3 1 0 2 0
3 2 2 0 2 40
4 4 4 1 0 80
2"""

# Write to file and run
with open('test_forced_collision_input.txt', 'w') as f:
    f.write(test_input)

print("Created test input file: test_forced_collision_input.txt")
print("Run with: python3 agent.py < test_forced_collision_input.txt")
