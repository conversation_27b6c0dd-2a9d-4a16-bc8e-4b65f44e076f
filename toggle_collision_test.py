#!/usr/bin/env python3
"""
Scrip<PERSON> to easily enable/disable collision testing mode in agent.py
"""

import sys
import re

def toggle_collision_testing(enable=None):
    """
    Toggle the ENABLE_COLLISION_TESTING flag in agent.py
    
    Args:
        enable: True to enable, False to disable, None to toggle current state
    """
    
    # Read the current file
    try:
        with open('agent.py', 'r') as f:
            content = f.read()
    except FileNotFoundError:
        print("ERROR: agent.py not found in current directory")
        return False
    
    # Find the current state
    pattern = r'ENABLE_COLLISION_TESTING = (True|False)'
    match = re.search(pattern, content)
    
    if not match:
        print("ERROR: Could not find ENABLE_COLLISION_TESTING flag in agent.py")
        return False
    
    current_state = match.group(1) == 'True'
    print(f"Current collision testing state: {'ENABLED' if current_state else 'DISABLED'}")
    
    # Determine new state
    if enable is None:
        new_state = not current_state
    else:
        new_state = enable
    
    if new_state == current_state:
        print(f"No change needed - collision testing is already {'ENABLED' if current_state else 'DISABLED'}")
        return True
    
    # Replace the flag
    new_content = re.sub(pattern, f'ENABLE_COLLISION_TESTING = {new_state}', content)
    
    # Write back to file
    try:
        with open('agent.py', 'w') as f:
            f.write(new_content)
        print(f"SUCCESS: Collision testing {'ENABLED' if new_state else 'DISABLED'}")
        
        if new_state:
            print("\nCollision testing is now ACTIVE!")
            print("The agent will attempt to create collision scenarios with 2 agents:")
            print("1. Move agents to positions (0,1) and (0,3)")
            print("2. Then simultaneously move them to (0,4) and (0,0)")
            print("3. Watch the debug output to see collision behavior")
            print("\nTo disable: python3 toggle_collision_test.py off")
        else:
            print("\nCollision testing is now DISABLED - normal strategy will be used")
            print("To re-enable: python3 toggle_collision_test.py on")
            
        return True
        
    except Exception as e:
        print(f"ERROR: Could not write to agent.py: {e}")
        return False

def main():
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ['on', 'enable', 'true', '1']:
            toggle_collision_testing(True)
        elif arg in ['off', 'disable', 'false', '0']:
            toggle_collision_testing(False)
        elif arg in ['toggle', 'switch']:
            toggle_collision_testing(None)
        else:
            print("Usage: python3 toggle_collision_test.py [on|off|toggle]")
            print("  on/enable/true/1  - Enable collision testing")
            print("  off/disable/false/0 - Disable collision testing") 
            print("  toggle/switch - Toggle current state")
            print("  (no argument) - Toggle current state")
    else:
        # No argument - toggle current state
        toggle_collision_testing(None)

if __name__ == "__main__":
    main()
