#!/usr/bin/env python3

# Test the exact server collision behavior
from collections import deque

def get_adjacent_tiles(x, y, width, height):
    """Get adjacent tiles (up, down, left, right)."""
    adjacent = []
    for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
        nx, ny = x + dx, y + dy
        if 0 <= nx < width and 0 <= ny < height:
            adjacent.append((nx, ny))
    return adjacent

def can_move_to_tile(x, y, grid, agents):
    """Check if a tile is passable (no cover, no other agents)."""
    # Check for cover
    tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
    if tile and tile['tile_type'] > 0:
        return False
    # Check for other agents
    for agent in agents:
        if agent['x'] == x and agent['y'] == y:
            return False
    return True

def has_valid_path(from_pos, to_pos, blocked_positions, grid, width, height):
    """Check if there's a valid path from from_pos to to_pos avoiding blocked positions."""
    if from_pos == to_pos:
        return True
    
    # Use BFS to find if path exists (similar to server's A*)
    queue = deque([from_pos])
    visited = {from_pos}
    
    while queue:
        current = queue.popleft()
        
        # Check all adjacent positions
        for next_pos in get_adjacent_tiles(current[0], current[1], width, height):
            if next_pos in visited:
                continue
                
            # Check if position is blocked by static agent
            if next_pos in blocked_positions:
                continue
                
            # Check if position has cover
            if not can_move_to_tile(next_pos[0], next_pos[1], grid, []):
                continue
                
            # Found target
            if next_pos == to_pos:
                return True
                
            visited.add(next_pos)
            queue.append(next_pos)
    
    return False

def detect_move_collisions_server_style(planned_moves, all_agents, grid, width, height):
    """Detect collisions exactly like the server does."""
    collisions = []
    move_list = list(planned_moves.items())

    # Simulate server's sequential processing for pathfinding
    moving_agent_ids = set(planned_moves.keys())
    static_positions = set()
    for agent in all_agents:
        if agent['agent_id'] not in moving_agent_ids:
            static_positions.add((agent['x'], agent['y']))

    # Process each moving agent sequentially (like server does)
    remaining_static = static_positions.copy()
    print(f"DEBUG: Initial static positions: {remaining_static}")

    for agent in all_agents:
        if agent['agent_id'] in planned_moves:
            agent_id = agent['agent_id']
            move = planned_moves[agent_id]
            from_pos = move['from']
            to_pos = move['to']

            print(f"DEBUG: Processing agent {agent_id}: {from_pos} -> {to_pos}")
            print(f"DEBUG: Current static positions: {remaining_static}")

            # Check if path is blocked by remaining static agents
            path_valid = has_valid_path(from_pos, to_pos, remaining_static, grid, width, height)
            print(f"DEBUG: Path valid: {path_valid}")

            if not path_valid:
                collisions.append({
                    'type': 'path_collision',
                    'agents': [agent_id],
                    'reason': f"Path from {from_pos} to {to_pos} blocked by static agent"
                })

            # Remove this agent from static positions (server does this)
            current_pos = (agent['x'], agent['y'])
            if current_pos in remaining_static:
                remaining_static.remove(current_pos)
                print(f"DEBUG: Removed {current_pos} from static positions")

    # Check for target and swap collisions between moving agents
    # NOTE: Server does NOT check for path crossings, only start/end positions
    for i, (agent_id1, move1) in enumerate(move_list):
        for j, (agent_id2, move2) in enumerate(move_list):
            if i >= j:  # Avoid checking same pair twice
                continue

            # Check for target collision (both moving to same destination)
            if move1['to'] == move2['to']:
                collisions.append({
                    'type': 'target_collision',
                    'agents': [agent_id1, agent_id2],
                    'reason': f"Both agents moving to {move1['to']}"
                })

            # Check for swap collision (agents swapping positions)
            elif move1['to'] == move2['from'] and move1['from'] == move2['to']:
                collisions.append({
                    'type': 'swap_collision',
                    'agents': [agent_id1, agent_id2],
                    'reason': f"Agents swapping positions {move1['from']} <-> {move1['to']}"
                })

    return collisions

def test_server_collision_behavior():
    print("Testing exact server collision behavior...")
    
    # Create a simple 4x4 grid
    grid = []
    for y in range(4):
        for x in range(4):
            grid.append({'x': x, 'y': y, 'tile_type': 0})
    
    width, height = 4, 4
    
    # Test case 1: Cross-path scenario (should NOT be detected as collision)
    print("\n=== Test 1: Cross-path scenario ===")
    all_agents = [
        {'agent_id': 1, 'x': 1, 'y': 1, 'wetness': 10},  # Moving (1,1) -> (3,1)
        {'agent_id': 2, 'x': 2, 'y': 0, 'wetness': 20},  # Moving (2,0) -> (2,2)
        {'agent_id': 3, 'x': 0, 'y': 0, 'wetness': 15},  # Static
    ]
    
    planned_moves = {
        1: {'from': (1, 1), 'to': (3, 1)},  # Horizontal move
        2: {'from': (2, 0), 'to': (2, 2)}   # Vertical move - crosses at (2,1)
    }
    
    collisions = detect_move_collisions_server_style(planned_moves, all_agents, grid, width, height)
    print(f"Cross-path collisions detected: {len(collisions)}")
    for c in collisions:
        print(f"  {c}")
    print("Expected: 0 collisions (server allows visual crossings)")
    
    # Test case 2: Sequential processing priority
    print("\n=== Test 2: Sequential processing priority ===")
    all_agents = [
        {'agent_id': 1, 'x': 1, 'y': 1, 'wetness': 10},  # First in list
        {'agent_id': 2, 'x': 2, 'y': 1, 'wetness': 20},  # Second in list  
        {'agent_id': 3, 'x': 3, 'y': 1, 'wetness': 15},  # Static blocker
    ]
    
    planned_moves = {
        1: {'from': (1, 1), 'to': (4, 1)},  # Wants to go past agent 2 and 3
        2: {'from': (2, 1), 'to': (4, 1)}   # Also wants to go past agent 3
    }
    
    # Note: Agent 3 at (3,1) blocks the path to (4,1) for both agents
    # But agent 1 is processed first, so agent 2 is removed from static list
    # when agent 1's path is checked
    
    collisions = detect_move_collisions_server_style(planned_moves, all_agents, grid, width, height)
    print(f"Sequential processing collisions: {len(collisions)}")
    for c in collisions:
        print(f"  {c}")
    print("Expected: Both should be blocked by static agent 3, plus target collision")

    # Test case 3: Processing order matters
    print("\n=== Test 3: Processing order dependency ===")
    all_agents = [
        {'agent_id': 1, 'x': 1, 'y': 1, 'wetness': 10},  # First in list
        {'agent_id': 2, 'x': 2, 'y': 1, 'wetness': 20},  # Second in list, blocks agent 1's path
    ]

    planned_moves = {
        1: {'from': (1, 1), 'to': (3, 1)},  # Wants to go through agent 2's position
        2: {'from': (2, 1), 'to': (0, 1)}   # Moving away, clearing the path
    }

    collisions = detect_move_collisions_server_style(planned_moves, all_agents, grid, width, height)
    print(f"Processing order collisions: {len(collisions)}")
    for c in collisions:
        print(f"  {c}")
    print("Expected: Agent 1 blocked initially, but agent 2 moves away")
    print("Reality: Agent 1 processed first, sees agent 2 as static blocker")

if __name__ == "__main__":
    test_server_collision_behavior()
