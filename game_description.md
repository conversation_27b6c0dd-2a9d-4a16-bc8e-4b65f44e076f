Water Fight Game Rules (Level 5)
Goal
Win the water fight by controlling the most territory or out-soaking your opponent!
Rules
The game is played on a grid. Each player controls a team of agents.
Agents
Agents are player-controlled units on the field with specific attributes and actions.

Wetness Meter: Each agent has a wetness meter that increases when attacked by enemy agents. If an agent’s wetness reaches 100, they are eliminated and removed from play.
Attributes:
soaking_power: Indicates how much wetness an agent deals when shooting.
optimal_range: Used to apply a penalty if the target is too far.
Up to optimal_range, shooting deals 100% of soaking_power.
Beyond optimal_range but within twice that range, shooting deals 50% of soaking_power.
Beyond twice the optimal_range, the shot fails.


Note: All distances are calculated using the Manhattan formula.


Shoot Cooldown: After shooting, an agent must wait shoot_cooldown turns before using the SHOOT action again, though other actions remain available.
Splash Bombs: Each agent has a set number of splash bombs, determined at the game’s start and varying between agents.

Actions
Each turn, you must output one command per agent, which can include at most one move action and one combat action. Actions can be specified in any order, but their execution follows a priority (see Action Order per Turn).

Move Action:
MOVE x y: The agent attempts to move to location (x, y). If the target is not orthogonally adjacent, the agent moves along the shortest valid path. Movement is cancelled if the target tile has cover, another agent, or if agents collide during movement.


Combat Actions:
SHOOT id: Shoots agent agentId, dealing wetness based on the shooter’s optimal_range and soaking_power. Damage is reduced by the target’s cover or hunkering (see Cover section).
THROW x y: Throws a splash bomb at location (x, y), up to a maximum distance of 4 tiles. Deals 30 wetness to the target tile and all adjacent tiles (orthogonally and diagonally), ignoring damage reduction from covers or hunkering.
HUNKER_DOWN: Grants 25% damage reduction against enemy shots for the turn, stackable with cover bonuses.



Cover
Each grid tile is provided via standard input with a tile_type:

0: Empty tile.
1: Low cover (impassable, provides 50% damage reduction against shots).
2: High cover (impassable, provides 75% damage reduction against shots).

Agents benefit from cover if orthogonally adjacent to it and the enemy shot comes from the opposite side. If both agents are adjacent to the same cover, it is ignored. When multiple covers apply, only the highest cover counts.
Note: Hunkering down stacks with cover, providing total protection of 75% (low cover) or 100% (high cover).
Points
Points are earned by controlling more tiles than your opponent:

A tile is controlled by the player whose agent is closest to it (using Manhattan distance).
If an agent’s wetness is 50 or higher, their distance to tiles is doubled for control calculations.
Each turn, if you control more tiles than your opponent, you score points equal to the difference in controlled tiles.

Action Order per Turn
Actions are executed synchronously, with the following priority:

MOVE actions.
HUNKER_DOWN actions.
SHOOT and THROW actions.
Removal of soaked agents (with wetness ≥ 100).

Victory Conditions
The winner is the player who:

Reaches 600 more points than their opponent.
Eliminates all opposing agents.
Has the most points after 100 turns.

Defeat Conditions
You lose if:

Your program does not provide a command within the allotted time.
Any command is invalid.

Game Protocol
Initialization Input

Line 1: myId (integer, your player ID).
Line 2: agentDataCount (integer, number of agents on the grid).
Next agentDataCount lines: For each agent:
agentId: Unique ID.
player: ID of the owning player.
shootCooldown: Minimum turns between shots.
optimalRange: Optimal shooting range.
soakingPower: Maximum wetness damage.
splashBombs: Starting number of splash bombs.


Next line: width and height (integers, grid size).
Next width * height lines: For each tile:
x: X coordinate (0 is leftmost).
y: Y coordinate (0 is uppermost).
tile_type: 0 (empty), 1 (low cover), or 2 (high cover).



Input for One Game Turn

Line 1: agentCount (integer, number of remaining agents).
Next agentCount lines: For each agent:
agentId: Unique ID.
x: X coordinate.
y: Y coordinate.
cooldown: Turns until the agent can shoot again.
splashBombs: Current number of splash bombs.
wetness: Current wetness level.


Next line: myAgentCount (integer, number of your agents).

Output
For each agent, output a single line with:

The agentId (optional; if omitted, actions are assigned in ascending agentId order).
Up to one move action: MOVE x y.
Up to one combat action: SHOOT id, THROW x y, or HUNKER_DOWN.
Up to one message action: MESSAGE text (for debugging, displayed in the viewer).

Actions are separated by semicolons. Example:
3;MOVE 12 3;SHOOT 5

This instructs agent 3 to move to (12, 3) and shoot agent 5.
Constraints

Response time per turn: ≤ 50ms.
Response time for first turn: ≤ 1000ms.
Grid size: 12 ≤ width ≤ 20, 6 ≤ height ≤ 10.
Agent count: 6 ≤ agentDataCount ≤ 10.

Debugging Tips

Hover over the grid to see tile information.
Use MESSAGE text to display debug text above an agent.
Press the gear icon in the viewer for extra display options.
Use keyboard controls: space to play/pause, arrows to step one frame at a time.
