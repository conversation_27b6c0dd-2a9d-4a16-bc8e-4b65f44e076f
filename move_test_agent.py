import sys

def manhattan_distance(x1, y1, x2, y2):
    return abs(x1 - x2) + abs(y1 - y2)

def can_move_to_tile(x, y, grid, agents):
    """Check if a tile is passable (no cover, no other agents)."""
    # Check bounds
    if x < 0 or y < 0:
        return False
    
    # Check for cover
    tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
    if tile and tile['tile_type'] > 0:
        return False
    
    # Check for other agents
    for agent in agents:
        if agent['x'] == x and agent['y'] == y:
            return False
    
    return True

def find_map_corners(width, height):
    """Find the four corners of the map."""
    return [
        (0, 0),           # top-left
        (0, height - 1),  # bottom-left  
        (width - 1, height - 1),  # bottom-right
        (width - 1, 0)    # top-right
    ]

def find_nearest_open_tile(target_x, target_y, grid, agents, width, height):
    """Find the nearest open tile to the target position using BFS."""
    if can_move_to_tile(target_x, target_y, grid, agents):
        return (target_x, target_y)
    
    # BFS to find nearest open tile
    from collections import deque
    queue = deque([(target_x, target_y, 0)])
    visited = {(target_x, target_y)}
    
    directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]
    
    while queue:
        x, y, dist = queue.popleft()
        
        for dx, dy in directions:
            nx, ny = x + dx, y + dy
            
            if (nx, ny) in visited:
                continue
            if nx < 0 or ny < 0 or nx >= width or ny >= height:
                continue
                
            visited.add((nx, ny))
            
            if can_move_to_tile(nx, ny, grid, agents):
                return (nx, ny)
            
            queue.append((nx, ny, dist + 1))
    
    # Fallback to current position if no open tile found
    return (target_x, target_y)

def find_perimeter_tile_past_corner(corner_x, corner_y, width, height, grid, agents, clockwise=True):
    """Find the next open tile along the map perimeter past the given corner."""
    corners = find_map_corners(width, height)
    corner_idx = corners.index((corner_x, corner_y))
    
    # Get the next corner in the direction we're moving
    if clockwise:
        next_corner_idx = (corner_idx + 1) % 4
    else:
        next_corner_idx = (corner_idx - 1) % 4
    
    next_corner = corners[next_corner_idx]
    
    # Walk along the edge from current corner toward next corner
    if corner_x == 0 and corner_y == 0:  # top-left
        if clockwise:  # go right along top edge
            for x in range(1, width):
                if can_move_to_tile(x, 0, grid, agents):
                    return (x, 0)
        else:  # go down along left edge
            for y in range(1, height):
                if can_move_to_tile(0, y, grid, agents):
                    return (0, y)
    elif corner_x == 0 and corner_y == height - 1:  # bottom-left
        if clockwise:  # go up along left edge
            for y in range(height - 2, -1, -1):
                if can_move_to_tile(0, y, grid, agents):
                    return (0, y)
        else:  # go right along bottom edge
            for x in range(1, width):
                if can_move_to_tile(x, height - 1, grid, agents):
                    return (x, height - 1)
    elif corner_x == width - 1 and corner_y == height - 1:  # bottom-right
        if clockwise:  # go left along bottom edge
            for x in range(width - 2, -1, -1):
                if can_move_to_tile(x, height - 1, grid, agents):
                    return (x, height - 1)
        else:  # go up along right edge
            for y in range(height - 2, -1, -1):
                if can_move_to_tile(width - 1, y, grid, agents):
                    return (width - 1, y)
    elif corner_x == width - 1 and corner_y == 0:  # top-right
        if clockwise:  # go down along right edge
            for y in range(1, height):
                if can_move_to_tile(width - 1, y, grid, agents):
                    return (width - 1, y)
        else:  # go left along top edge
            for x in range(width - 2, -1, -1):
                if can_move_to_tile(x, 0, grid, agents):
                    return (x, 0)
    
    # Fallback to the corner itself if no open tile found
    return (corner_x, corner_y)

def execute_movement_test(my_agents, enemy_agents, grid, width, height, turn_count):
    """Execute the movement test strategy."""
    agent_commands = {}
    corners = find_map_corners(width, height)
    
    print(f"DEBUG: Movement test turn {turn_count} with {len(my_agents)} agents", file=sys.stderr)
    print(f"DEBUG: Map corners: {corners}", file=sys.stderr)
    
    # Sort agents by ID for consistent behavior
    sorted_agents = sorted(my_agents, key=lambda x: x['agent_id'])
    
    for i, agent in enumerate(sorted_agents):
        agent_pos = (agent['x'], agent['y'])
        print(f"DEBUG: Agent {agent['agent_id']} at {agent_pos}", file=sys.stderr)
        
        if turn_count == 0:
            # First turn: send each agent to a different corner
            target_corner = corners[i % len(corners)]
            target_pos = find_nearest_open_tile(target_corner[0], target_corner[1], grid, my_agents + enemy_agents, width, height)
            print(f"DEBUG: Agent {agent['agent_id']} targeting corner {target_corner} -> {target_pos}", file=sys.stderr)
            
        elif 1 <= turn_count <= 4:
            # Turns 1-4: rotate counter-clockwise around corners
            # Each agent moves to the "next" corner in counter-clockwise order
            current_corner_idx = (i + turn_count - 1) % len(corners)
            target_corner = corners[current_corner_idx]
            target_pos = find_nearest_open_tile(target_corner[0], target_corner[1], grid, my_agents + enemy_agents, width, height)
            print(f"DEBUG: Agent {agent['agent_id']} counter-clockwise to corner {target_corner} -> {target_pos}", file=sys.stderr)
            
        elif 5 <= turn_count <= 8:
            # Turns 5-8: rotate clockwise, but look past corner if occupied
            clockwise_turn = turn_count - 4
            current_corner_idx = (i - clockwise_turn) % len(corners)
            target_corner = corners[current_corner_idx]
            
            if can_move_to_tile(target_corner[0], target_corner[1], grid, my_agents + enemy_agents):
                target_pos = target_corner
                print(f"DEBUG: Agent {agent['agent_id']} clockwise to corner {target_corner}", file=sys.stderr)
            else:
                target_pos = find_perimeter_tile_past_corner(target_corner[0], target_corner[1], width, height, grid, my_agents + enemy_agents, clockwise=True)
                print(f"DEBUG: Agent {agent['agent_id']} clockwise past corner {target_corner} -> {target_pos}", file=sys.stderr)
        
        else:
            # After turn 8: just hunker down
            target_pos = agent_pos
            print(f"DEBUG: Agent {agent['agent_id']} hunkering down at {target_pos}", file=sys.stderr)
        
        # Generate move command
        if target_pos != agent_pos:
            command = f"{agent['agent_id']};MOVE {target_pos[0]} {target_pos[1]};MESSAGE MovTest-T{turn_count}"
        else:
            command = f"{agent['agent_id']};HUNKER_DOWN;MESSAGE MovTest-T{turn_count}-Stay"
        
        agent_commands[agent['agent_id']] = command
    
    return agent_commands

# Read initialization input
my_id = int(input())
agent_data_count = int(input())
agents_init = []
for _ in range(agent_data_count):
    agent_id, player, shoot_cooldown, optimal_range, soaking_power, splash_bombs = map(int, input().split())
    agents_init.append({
        'agent_id': agent_id,
        'player': player,
        'shoot_cooldown': shoot_cooldown,
        'optimal_range': optimal_range,
        'soaking_power': soaking_power,
        'splash_bombs': splash_bombs
    })

width, height = map(int, input().split())
grid = []
for i in range(height):
    inputs = input().split()
    for j in range(width):
        x = int(inputs[3*j])
        y = int(inputs[3*j+1])
        tile_type = int(inputs[3*j+2])
        grid.append({'x': x, 'y': y, 'tile_type': tile_type})

print(f"DEBUG: Initialized movement test agent for player {my_id}", file=sys.stderr)
print(f"DEBUG: Map size: {width}x{height}", file=sys.stderr)
print(f"DEBUG: Agent count: {agent_data_count}", file=sys.stderr)

# Main game loop
turn_count = 0
while True:
    try:
        agent_count = int(input())
        agents = []
        for _ in range(agent_count):
            agent_id, x, y, cooldown, splash_bombs, wetness = map(int, input().split())
            agents.append({
                'agent_id': agent_id,
                'x': x,
                'y': y,
                'cooldown': cooldown,
                'splash_bombs': splash_bombs,
                'wetness': wetness
            })
        my_agent_count = int(input())

        # Find my agents and enemy agents
        my_agents = [agent for agent in agents if any(init_agent['agent_id'] == agent['agent_id'] and init_agent['player'] == my_id for init_agent in agents_init)]
        enemy_agents = [agent for agent in agents if any(init_agent['agent_id'] == agent['agent_id'] and init_agent['player'] != my_id for init_agent in agents_init)]

        # Execute movement test strategy
        agent_commands = execute_movement_test(my_agents, enemy_agents, grid, width, height, turn_count)

        # Output commands in agent ID order
        for my_agent in sorted(my_agents, key=lambda x: x['agent_id']):
            print(agent_commands[my_agent['agent_id']])

        turn_count += 1

    except EOFError:
        # Game ended
        break
    except Exception as e:
        print(f"ERROR: {e}", file=sys.stderr)
        break
