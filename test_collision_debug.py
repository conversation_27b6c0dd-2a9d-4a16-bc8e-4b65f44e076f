#!/usr/bin/env python3

# Test collision detection logic directly
import sys
sys.path.append('.')
from agent import detect_move_collisions, resolve_move_collisions

def test_collision_detection():
    print("Testing collision detection...")

    # Create a simple grid for testing
    grid = [
        {'x': 0, 'y': 0, 'tile_type': 0},
        {'x': 1, 'y': 0, 'tile_type': 0},
        {'x': 2, 'y': 0, 'tile_type': 0},
        {'x': 3, 'y': 0, 'tile_type': 0},
        {'x': 0, 'y': 1, 'tile_type': 0},
        {'x': 1, 'y': 1, 'tile_type': 0},
        {'x': 2, 'y': 1, 'tile_type': 0},
        {'x': 3, 'y': 1, 'tile_type': 0},
        {'x': 0, 'y': 2, 'tile_type': 0},
        {'x': 1, 'y': 2, 'tile_type': 0},
        {'x': 2, 'y': 2, 'tile_type': 0},
        {'x': 3, 'y': 2, 'tile_type': 0},
        {'x': 0, 'y': 3, 'tile_type': 0},
        {'x': 1, 'y': 3, 'tile_type': 0},
        {'x': 2, 'y': 3, 'tile_type': 0},
        {'x': 3, 'y': 3, 'tile_type': 0},
    ]
    width, height = 4, 4

    # Test agents
    all_agents = [
        {'agent_id': 1, 'x': 2, 'y': 1, 'wetness': 10},  # Moving agent
        {'agent_id': 2, 'x': 2, 'y': 2, 'wetness': 20},  # Static agent blocking path
        {'agent_id': 3, 'x': 1, 'y': 1, 'wetness': 15},  # Another moving agent
        {'agent_id': 4, 'x': 3, 'y': 3, 'wetness': 25}   # Another moving agent
    ]

    # Test case 1: Path collision (agent 1 wants to move from (2,1) to (2,3) but agent 2 is at (2,2))
    planned_moves1 = {
        1: {'from': (2, 1), 'to': (2, 3)}  # Blocked by agent 2 at (2,2)
    }

    collisions1 = detect_move_collisions(planned_moves1, all_agents, grid, width, height)
    print(f"Test 1 - Path collision: {collisions1}")

    # Test case 2: Target collision (both agents moving to same destination)
    planned_moves2 = {
        1: {'from': (2, 1), 'to': (1, 2)},
        3: {'from': (1, 1), 'to': (1, 2)}  # Same destination
    }

    collisions2 = detect_move_collisions(planned_moves2, all_agents, grid, width, height)
    print(f"Test 2 - Target collision: {collisions2}")

    # Test case 3: Swap collision (agents swapping positions)
    planned_moves3 = {
        1: {'from': (2, 1), 'to': (1, 1)},
        3: {'from': (1, 1), 'to': (2, 1)}  # Swapping positions
    }

    collisions3 = detect_move_collisions(planned_moves3, all_agents, grid, width, height)
    print(f"Test 3 - Swap collision: {collisions3}")

    # Test case 4: No collision (valid moves)
    planned_moves4 = {
        1: {'from': (2, 1), 'to': (1, 1)},  # Valid move
        4: {'from': (3, 3), 'to': (3, 2)}   # Valid move
    }

    collisions4 = detect_move_collisions(planned_moves4, all_agents, grid, width, height)
    print(f"Test 4 - No collision: {collisions4}")

    # Test collision resolution
    print("\nTesting collision resolution:")
    resolved, cancelled = resolve_move_collisions(all_agents, planned_moves1, grid, width, height)
    print(f"Path collision resolution - Resolved: {resolved}")
    print(f"Path collision resolution - Cancelled: {cancelled}")

if __name__ == "__main__":
    test_collision_detection()
